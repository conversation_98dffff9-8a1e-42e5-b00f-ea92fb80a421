<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="Description" content="Papua New Guinea's First AI-Powered Government Recruitment System" />
  <title><?= isset($title) ? $title . ' - ' : '' ?>DERS - Dakoii Echad Recruitment & Selection System</title>

  <!-- Standard favicon -->
  <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">

  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#F00F00">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="DERS">
  <meta name="application-name" content="DERS">
  <meta name="msapplication-TileColor" content="#F00F00">
  <meta name="msapplication-config" content="<?= base_url() ?>public/browserconfig.xml">

  <!-- PWA Manifest -->
  <link rel="manifest" href="<?= base_url() ?>public/manifest.json">

  <!-- PWA Icons -->
  <link rel="apple-touch-icon" sizes="72x72" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-72x72.png">
  <link rel="apple-touch-icon" sizes="96x96" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="128x128" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-128x128.png">
  <link rel="apple-touch-icon" sizes="144x144" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-144x144.png">
  <link rel="apple-touch-icon" sizes="152x152" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="192x192" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-192x192.png">
  <link rel="apple-touch-icon" sizes="384x384" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-384x384.png">
  <link rel="apple-touch-icon" sizes="512x512" href="<?= base_url() ?>public/assets/system_img/pwa-icons/icon-512x512.png">

  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- Source Sans Pro - clean, elegant, professional font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <!-- Select2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <!-- PWA Install Button Styles -->
  <style>
    .pwa-install-banner {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, var(--red), var(--black));
      color: white;
      padding: 15px 25px;
      border-radius: 50px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 1050;
      display: none;
      align-items: center;
      gap: 15px;
      max-width: 90%;
      backdrop-filter: blur(10px);
    }

    .pwa-install-banner.show {
      display: flex;
    }

    .pwa-install-btn {
      background: var(--yellow);
      color: var(--black);
      border: none;
      padding: 8px 16px;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .pwa-install-btn:hover {
      background: var(--yellow-dark);
      transform: translateY(-2px);
    }

    .pwa-close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 5px;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .pwa-close-btn:hover {
      background: rgba(255,255,255,0.2);
    }

    @media (max-width: 768px) {
      .pwa-install-banner {
        bottom: 10px;
        left: 10px;
        right: 10px;
        transform: none;
        max-width: none;
        padding: 12px 20px;
      }
    }
  </style>

  <!-- Page specific CSS -->
  <?= $this->renderSection('css') ?>

  <style>
    :root {
      /* Primary colors from the PNG flag color scheme */
      --red: #F00F00;             /* Red from PNG flag */
      --red-dark: #D00D00;        /* Darker red for hover states */
      --yellow: #FFC20F;          /* Yellow/Gold from PNG flag */
      --yellow-dark: #E6B00E;     /* Darker yellow for hover states */
      --black: #000000;           /* Black from PNG flag */
      --gray: #BFC1C7;            /* Light gray from the palette */
      --gray-dark: #9FA1A7;       /* Darker gray for hover states */
      --white: #FFFFFF;           /* White from the palette */

      /* UI colors */
      --text-primary: #000000;    /* Primary text color */
      --text-secondary: #333333;  /* Secondary text color */
      --text-light: #FFFFFF;      /* Light text color for dark backgrounds */
      --bg-light: #FFFFFF;        /* Light background */
      --bg-dark: #000000;         /* Dark background */
    }

    /* Optimize font loading behavior */
    @font-face {
      font-family: 'Source Sans 3';
      font-display: swap;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
      scroll-behavior: smooth;
      color: var(--text-primary);
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #f8f9fa;
    }

    html {
      scroll-behavior: smooth;
    }

    h1, h2, h3, h4, h5, h6 {
      font-family: 'Source Sans 3', sans-serif;
      font-weight: 600;
      line-height: 1.3;
      color: var(--red);
    }

    /* Fix for dark text on dark background */
    .gradient-bg h1,
    .gradient-bg h2,
    .gradient-bg h3,
    .gradient-bg h4,
    .gradient-bg h5,
    .gradient-bg h6,
    .gradient-bg .text-white-75 {
      color: var(--white) !important;
    }

    p {
      font-weight: 400;
      margin-bottom: 1.5rem;
    }

    .gradient-bg {
      background:
        radial-gradient(ellipse at bottom right, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 30%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.1) 70%, transparent 85%),
        linear-gradient(135deg, var(--red), var(--red-dark));
      position: relative;
      overflow: hidden;
    }

    .gradient-bg::after {
      content: '';
      position: absolute;
      bottom: -50px;
      left: 0;
      width: 100%;
      height: 100px;
      background: var(--yellow);
      transform: skewY(-2deg);
      z-index: 1;
    }

    .content-wrapper {
      position: relative;
      z-index: 2;
      padding: 3rem 0;
    }

    /* Navbar styles */
    .navbar {
      background-color: var(--black) !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 0.8rem 0;
    }

    .navbar-brand {
      color: var(--white) !important;
      text-decoration: none;
    }

    .navbar-brand:hover {
      color: var(--yellow) !important;
    }

    .navbar-toggler {
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 0.4rem 0.6rem;
    }

    .navbar-toggler:focus {
      box-shadow: 0 0 0 0.2rem rgba(255, 194, 15, 0.25);
    }

    .navbar-toggler-icon {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }

    .nav-link {
      color: rgba(255, 255, 255, 0.9) !important;
      transition: all 0.3s ease;
      padding: 0.6rem 1rem;
      font-weight: 500;
      position: relative;
    }

    .nav-link:hover {
      color: var(--yellow) !important;
    }

    .nav-link.active {
      color: var(--yellow) !important;
      font-weight: 600;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: var(--red);
      transition: all 0.3s ease;
      transform: translateX(-50%);
      opacity: 0;
    }

    .nav-link:hover::after,
    .nav-link.active::after {
      width: 70%;
      opacity: 1;
    }

    /* Mobile navigation styles */
    @media (max-width: 991.98px) {
      .navbar-collapse {
        background-color: var(--black);
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .navbar-nav {
        text-align: center;
      }

      .nav-item {
        margin: 0.2rem 0;
      }

      .nav-link {
        padding: 0.8rem 1rem;
        border-radius: 6px;
        margin: 0.1rem 0;
      }

      .nav-link:hover,
      .nav-link.active {
        background-color: rgba(255, 194, 15, 0.1);
      }

      .nav-link::after {
        display: none;
      }

      .navbar-brand span {
        font-size: 1.5rem;
      }
    }

    /* Ensure proper spacing on mobile */
    @media (max-width: 576px) {
      .navbar-brand span {
        font-size: 1.3rem;
      }

      .navbar-brand img {
        height: 40px !important;
      }
    }

    /* Button styles */
    .btn {
      font-weight: 500;
      letter-spacing: 0.02em;
      padding: 0.6rem 1.5rem;
      transition: all 0.3s ease;
    }

    .btn-red {
      background-color: var(--red);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(240, 15, 0, 0.2);
    }

    .btn-red:hover {
      background-color: var(--red-dark);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(240, 15, 0, 0.3);
    }

    .btn-yellow {
      background-color: var(--yellow);
      color: var(--black);
      border: none;
      box-shadow: 0 4px 12px rgba(255, 194, 15, 0.2);
    }

    .btn-yellow:hover {
      background-color: var(--yellow-dark);
      color: var(--black);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 194, 15, 0.3);
    }

    .btn-black {
      background-color: var(--black);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .btn-black:hover {
      background-color: #333333;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    }

    /* Card styles */
    .card {
      border: none;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
    }

    /* Form control styles */
    input.form-control, .form-select {
      padding: 0.8rem 1.2rem;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }

    input.form-control:focus, .form-select:focus {
      border-color: var(--red);
      box-shadow: 0 0 0 3px rgba(240, 15, 0, 0.2);
    }

    /* Utility classes */
    .text-red { color: var(--red) !important; }
    .text-yellow { color: var(--yellow) !important; }
    .text-gray { color: var(--gray) !important; }
    .bg-red { background-color: var(--red) !important; }
    .bg-yellow { background-color: var(--yellow) !important; }
    .bg-gray { background-color: var(--gray) !important; }

    /* Legacy class names for compatibility */
    .text-navy { color: var(--red) !important; }
    .bg-navy { background-color: var(--red) !important; }
    .text-accent-red { color: var(--yellow) !important; }
    .bg-accent-red { background-color: var(--yellow) !important; }

    .hover-text-yellow:hover {
      color: var(--yellow) !important;
      transition: color 0.3s ease;
    }
  </style>
</head>

<body class="<?= isset($body_class) ? $body_class : 'bg-light' ?>">
  <!-- Navigation Menu -->
  <nav class="navbar navbar-dark navbar-expand-lg <?= isset($navbar_fixed) && $navbar_fixed ? 'fixed-top' : '' ?>">
    <div class="container">
      <!-- Brand/Logo -->
      <a class="navbar-brand d-flex align-items-center gap-3" href="<?= base_url() ?>">
        <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="DERS Logo" style="height: 48px;">
        <span class="h4 mb-0 fw-bold text-white">DERS</span>
      </a>

      <!-- Mobile menu toggle button -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
              aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation Links -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a href="<?= base_url() ?>" class="nav-link <?= (isset($menu) && $menu == 'home') ? 'active' : '' ?>">Home</a>
          </li>
          <li class="nav-item">
            <a href="<?= base_url('jobs') ?>" class="nav-link <?= (isset($menu) && $menu == 'jobs') ? 'active' : '' ?>">Jobs</a>
          </li>
          <li class="nav-item">
            <a href="<?= base_url('how-to-apply') ?>" class="nav-link <?= (isset($menu) && $menu == 'how_to_apply') ? 'active' : '' ?>">How to Apply</a>
          </li>
          <li class="nav-item">
            <a href="<?= base_url('applicant/login') ?>" class="nav-link <?= (isset($menu) && ($menu == 'applicant_login' || $menu == 'register')) ? 'active' : '' ?>">Apply</a>
          </li>
          <li class="nav-item">
            <a href="<?= base_url('login') ?>" class="nav-link <?= (isset($menu) && $menu == 'login') ? 'active' : '' ?>">Admin</a>
          </li>
          <li class="nav-item">
            <a href="<?= base_url('about') ?>" class="nav-link <?= (isset($menu) && $menu == 'about') ? 'active' : '' ?>">About</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="<?= isset($navbar_fixed) && $navbar_fixed ? 'pt-5' : '' ?>">
    <?= $this->renderSection('content') ?>
  </main>

  <!-- Footer Section -->
  <footer class="bg-black text-white py-5 mt-5">
    <div class="container">
      <div class="row align-items-center gy-4">
        <div class="col-md-4 text-center text-md-start">
          <a href="https://www.dakoiims.com" target="_blank" class="d-inline-block mb-3">
            <img src="<?= base_url() ?>/public/assets/system_img/dakoii-systems-logo.png" alt="Dakoii Systems Logo" class="img-fluid" style="max-height: 60px;">
          </a>
          <p class="small mb-0 text-white-50">Innovative software solutions for government and enterprise.</p>
        </div>
        <div class="col-md-4 text-center">
          <h5 class="text-yellow mb-3">Partners in Excellence</h5>
          <a href="#" class="d-inline-block mb-3">
            <img src="<?= base_url() ?>/public/assets/system_img/echad-logo.png" alt="Echad Consultancy Services Logo" class="img-fluid" style="max-height: 60px;">
          </a>
        </div>
        <div class="col-md-4 text-center text-md-end">
          <h5 class="text-yellow mb-3">Contact</h5>
          <p class="small mb-1"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></p>
          <p class="small mb-1"><i class="bi bi-headset me-2"></i> <EMAIL></p>
          <p class="small mb-3"><i class="bi bi-globe me-2"></i> www.dakoiims.com</p>
          <div class="d-flex justify-content-center justify-content-md-end gap-3">
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-facebook fs-5"></i></a>
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-twitter fs-5"></i></a>
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-linkedin fs-5"></i></a>
          </div>
        </div>
      </div>
      <hr class="my-4 border-secondary">
      <div class="row">
        <div class="col-12 text-center">
          <p class="mb-0 small">
            &copy; <?= date('Y') ?> Developed by
            <a href="https://www.dakoiims.com" class="text-yellow text-decoration-none" target="_blank"> <strong>Dakoii Systems</strong> </a>
            in collaboration with <strong>Echad Consultancy Services</strong>. <span class="align-right">Powered by <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></span>
          </p>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- SweetAlert Messages -->
  <?php if (session()->has('swal_icon')): ?>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      Swal.fire({
        icon: '<?= session()->getFlashdata('swal_icon') ?>',
        title: '<?= session()->getFlashdata('swal_title') ?>',
        text: '<?= session()->getFlashdata('swal_text') ?>',
        confirmButtonColor: '#F00F00'
      });
    });
  </script>
  <?php endif; ?>

  <!-- PWA Install Banner -->
  <div id="pwaInstallBanner" class="pwa-install-banner">
    <div>
      <strong>Install DERS App</strong>
      <div class="small">Get quick access to government jobs on your device</div>
    </div>
    <button id="pwaInstallBtn" class="pwa-install-btn">
      <i class="bi bi-download me-1"></i> Install
    </button>
    <button id="pwaCloseBtn" class="pwa-close-btn">
      <i class="bi bi-x"></i>
    </button>
  </div>

  <!-- PWA Service Worker Registration -->
  <script>
    // Service Worker Registration
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('<?= base_url() ?>sw.js', {
          scope: '<?= base_url() ?>'
        })
        .then(function(registration) {
          console.log('Service Worker registered successfully:', registration.scope);

          // Check for updates
          registration.addEventListener('updatefound', function() {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', function() {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content is available, show update notification
                if (confirm('New version available! Click OK to update.')) {
                  newWorker.postMessage({ type: 'SKIP_WAITING' });
                  window.location.reload();
                }
              }
            });
          });
        })
        .catch(function(error) {
          console.log('Service Worker registration failed:', error);
        });
      });
    }

    // PWA Install Prompt
    let deferredPrompt;
    let installBanner = document.getElementById('pwaInstallBanner');
    let installBtn = document.getElementById('pwaInstallBtn');
    let closeBtn = document.getElementById('pwaCloseBtn');

    // Listen for the beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', function(e) {
      console.log('PWA: beforeinstallprompt event fired');

      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();

      // Save the event so it can be triggered later
      deferredPrompt = e;

      // Check if user has previously dismissed the banner
      if (!localStorage.getItem('pwa-install-dismissed')) {
        // Show the install banner
        setTimeout(function() {
          installBanner.classList.add('show');
        }, 3000); // Show after 3 seconds
      }
    });

    // Handle install button click
    installBtn.addEventListener('click', function() {
      console.log('PWA: Install button clicked');

      if (deferredPrompt) {
        // Hide the banner
        installBanner.classList.remove('show');

        // Show the install prompt
        deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        deferredPrompt.userChoice.then(function(choiceResult) {
          if (choiceResult.outcome === 'accepted') {
            console.log('PWA: User accepted the install prompt');
          } else {
            console.log('PWA: User dismissed the install prompt');
          }
          deferredPrompt = null;
        });
      }
    });

    // Handle close button click
    closeBtn.addEventListener('click', function() {
      installBanner.classList.remove('show');
      // Remember that user dismissed the banner
      localStorage.setItem('pwa-install-dismissed', 'true');
    });

    // Listen for the app being installed
    window.addEventListener('appinstalled', function(e) {
      console.log('PWA: App was installed successfully');
      installBanner.classList.remove('show');

      // Show success message
      if (typeof Swal !== 'undefined') {
        Swal.fire({
          icon: 'success',
          title: 'App Installed!',
          text: 'DERS has been installed on your device. You can now access it from your home screen.',
          confirmButtonColor: '#F00F00'
        });
      }
    });

    // Check if app is already installed (standalone mode)
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      console.log('PWA: App is running in standalone mode');
      // Hide install banner if app is already installed
      installBanner.style.display = 'none';
    }

    // iOS Safari specific handling
    if (navigator.userAgent.match(/iPhone|iPad|iPod/)) {
      if (!window.navigator.standalone && !localStorage.getItem('pwa-install-dismissed')) {
        // Show iOS-specific install instructions
        setTimeout(function() {
          if (typeof Swal !== 'undefined') {
            Swal.fire({
              title: 'Install DERS App',
              html: 'To install this app on your iOS device:<br><br>' +
                    '1. Tap the <strong>Share</strong> button <i class="bi bi-share"></i><br>' +
                    '2. Select <strong>"Add to Home Screen"</strong> <i class="bi bi-plus-square"></i><br>' +
                    '3. Tap <strong>"Add"</strong> to install',
              icon: 'info',
              confirmButtonText: 'Got it!',
              confirmButtonColor: '#F00F00',
              showCancelButton: true,
              cancelButtonText: "Don't show again"
            }).then((result) => {
              if (result.dismiss === Swal.DismissReason.cancel) {
                localStorage.setItem('pwa-install-dismissed', 'true');
              }
            });
          }
        }, 5000);
      }
    }
  </script>

  <!-- DERS AI Assistant Chatbot -->
  <?= $this->include('components/chatbot') ?>

  <!-- Page specific scripts -->
  <?= $this->renderSection('scripts') ?>
</body>

</html>
